<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FloorPlan Cleaner</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <header class="header">
            <h1>FloorPlan Cleaner</h1>
            <p class="subtitle">Remove clutter, keep essentials: walls, doors, and windows</p>
        </header>

        <!-- Upload Section -->
        <section class="upload-section">
            <div class="upload-area" id="uploadArea">
                <div class="upload-content">
                    <div class="upload-icon">📁</div>
                    <h3>Drop your floorplan image here</h3>
                    <p>or <button type="button" class="btn btn--primary" id="browseBtn">Browse Files</button></p>
                    <small>Supports JPG, PNG, GIF, WEBP up to 2MB</small>
                </div>
                <input type="file" id="fileInput" accept="image/*" hidden>
            </div>
            <div class="upload-status" id="uploadStatus"></div>
        </section>

        <!-- Processing Controls -->
        <section class="controls-section" id="controlsSection" style="display: none;">
            <div class="card">
                <div class="card__header">
                    <h3>Processing Controls</h3>
                    <div class="mode-selector">
                        <label class="form-label">Processing Mode:</label>
                        <select class="form-control" id="processingMode">
                            <option value="basic" selected>Basic Clean</option>
                            <option value="advanced">Advanced Clean</option>
                            <option value="preserve">Preserve Details</option>
                        </select>
                    </div>
                </div>
                <div class="card__body">
                    <div class="controls-grid">
                        <div class="control-group">
                            <label class="form-label">Threshold Level: <span id="thresholdValue">127</span></label>
                            <input type="range" class="slider" id="threshold" min="0" max="255" value="127">
                        </div>
                        <div class="control-group">
                            <label class="form-label">Blur Intensity: <span id="blurValue">2</span></label>
                            <input type="range" class="slider" id="blurRadius" min="0" max="10" value="2">
                        </div>
                        <div class="control-group">
                            <label class="form-label">Erosion Size: <span id="erosionValue">3</span></label>
                            <input type="range" class="slider" id="erosionSize" min="1" max="10" value="3">
                        </div>
                        <div class="control-group">
                            <label class="form-label">Dilation Size: <span id="dilationValue">3</span></label>
                            <input type="range" class="slider" id="dilationSize" min="1" max="10" value="3">
                        </div>
                        <div class="control-group">
                            <label class="form-label">Min Component Size: <span id="componentValue">100</span></label>
                            <input type="range" class="slider" id="minComponentSize" min="10" max="1000" value="100">
                        </div>
                    </div>
                    <div class="control-buttons">
                        <button type="button" class="btn btn--secondary" id="resetBtn">Reset to Defaults</button>
                        <button type="button" class="btn btn--primary" id="processBtn">Process Image</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Results Display -->
        <section class="results-section" id="resultsSection" style="display: none;">
            <div class="results-grid">
                <div class="canvas-container">
                    <h4>Original Image</h4>
                    <canvas id="originalCanvas" class="image-canvas"></canvas>
                </div>
                <div class="canvas-container">
                    <h4>Processed Image</h4>
                    <canvas id="processedCanvas" class="image-canvas"></canvas>
                    <div class="processing-indicator" id="processingIndicator" style="display: none;">
                        <div class="spinner"></div>
                        <p>Processing...</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Download Section -->
        <section class="download-section" id="downloadSection" style="display: none;">
            <div class="card">
                <div class="card__body">
                    <div class="download-controls">
                        <button type="button" class="btn btn--primary btn--lg" id="downloadBtn">
                            Download Cleaned Floorplan
                        </button>
                        <button type="button" class="btn btn--secondary" id="newImageBtn">
                            Process Another Image
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Error Messages -->
        <div class="error-message" id="errorMessage" style="display: none;">
            <div class="card">
                <div class="card__body">
                    <div class="status status--error">
                        <span id="errorText"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>