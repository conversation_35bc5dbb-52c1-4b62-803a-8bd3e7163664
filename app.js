// FloorPlan Cleaner Application
class FloorPlanCleaner {
    constructor() {
        this.originalImage = null;
        this.originalImageData = null;
        this.processedImageData = null;
        this.settings = {
            threshold: 127,
            blurRadius: 2,
            erosionSize: 3,
            dilationSize: 3,
            minComponentSize: 100,
            processingMode: 'basic'
        };
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.updateSliderValues();
    }
    
    bindEvents() {
        // Upload functionality
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const browseBtn = document.getElementById('browseBtn');
        
        browseBtn.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => this.handleFileSelect(e.target.files[0]));
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const file = e.dataTransfer.files[0];
            if (file) this.handleFileSelect(file);
        });
        
        uploadArea.addEventListener('click', () => fileInput.click());
        
        // Control sliders
        const sliders = ['threshold', 'blurRadius', 'erosionSize', 'dilationSize', 'minComponentSize'];
        sliders.forEach(slider => {
            const element = document.getElementById(slider);
            element.addEventListener('input', (e) => {
                this.settings[slider] = parseInt(e.target.value);
                this.updateSliderValues();
                this.debounceProcess();
            });
        });
        
        // Processing mode
        document.getElementById('processingMode').addEventListener('change', (e) => {
            this.settings.processingMode = e.target.value;
            this.processImage();
        });
        
        // Buttons
        document.getElementById('resetBtn').addEventListener('click', () => this.resetSettings());
        document.getElementById('processBtn').addEventListener('click', () => this.processImage());
        document.getElementById('downloadBtn').addEventListener('click', () => this.downloadImage());
        document.getElementById('newImageBtn').addEventListener('click', () => this.resetApplication());
    }
    
    handleFileSelect(file) {
        if (!file) return;
        
        // Validate file type
        const supportedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!supportedTypes.includes(file.type)) {
            this.showError('Please select a valid image file (JPG, PNG, GIF, WEBP)');
            return;
        }
        
        // Validate file size (2MB limit)
        if (file.size > 2 * 1024 * 1024) {
            this.showError('File size must be less than 2MB');
            return;
        }
        
        this.showStatus('Loading image...', 'info');
        
        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                this.originalImage = img;
                this.loadImageToCanvas();
                this.showControls();
                this.processImage();
                this.hideError();
                this.showStatus('Image loaded successfully!', 'success');
            };
            img.onerror = () => {
                this.showError('Failed to load image. Please try another file.');
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
    
    loadImageToCanvas() {
        const canvas = document.getElementById('originalCanvas');
        const ctx = canvas.getContext('2d');
        
        // Scale image to fit while maintaining aspect ratio
        const maxSize = 512;
        let { width, height } = this.originalImage;
        
        if (width > maxSize || height > maxSize) {
            const ratio = Math.min(maxSize / width, maxSize / height);
            width *= ratio;
            height *= ratio;
        }
        
        canvas.width = width;
        canvas.height = height;
        
        ctx.drawImage(this.originalImage, 0, 0, width, height);
        this.originalImageData = ctx.getImageData(0, 0, width, height);
    }
    
    processImage() {
        if (!this.originalImageData) return;
        
        this.showProcessingIndicator(true);
        
        // Use setTimeout to allow UI to update
        setTimeout(() => {
            try {
                let imageData = this.cloneImageData(this.originalImageData);
                
                switch (this.settings.processingMode) {
                    case 'basic':
                        imageData = this.basicClean(imageData);
                        break;
                    case 'advanced':
                        imageData = this.advancedClean(imageData);
                        break;
                    case 'preserve':
                        imageData = this.preserveDetailsClean(imageData);
                        break;
                }
                
                this.displayProcessedImage(imageData);
                this.processedImageData = imageData;
                this.showDownloadSection();
                this.showProcessingIndicator(false);
            } catch (error) {
                this.showError('Processing failed: ' + error.message);
                this.showProcessingIndicator(false);
            }
        }, 100);
    }
    
    basicClean(imageData) {
        // Convert to grayscale
        imageData = this.convertToGrayscale(imageData);
        
        // Apply Gaussian blur
        if (this.settings.blurRadius > 0) {
            imageData = this.gaussianBlur(imageData, this.settings.blurRadius);
        }
        
        // Apply threshold
        imageData = this.threshold(imageData, this.settings.threshold);
        
        // Morphological operations
        imageData = this.morphologyErosion(imageData, this.settings.erosionSize);
        imageData = this.morphologyDilation(imageData, this.settings.dilationSize);
        
        // Remove small components
        imageData = this.removeSmallComponents(imageData, this.settings.minComponentSize);
        
        return imageData;
    }
    
    advancedClean(imageData) {
        // Start with basic cleaning
        imageData = this.basicClean(imageData);
        
        // Apply edge detection to preserve important lines
        const edges = this.detectEdges(imageData);
        imageData = this.combineWithEdges(imageData, edges);
        
        // Additional morphological operations
        imageData = this.morphologyOpening(imageData, 2);
        imageData = this.morphologyClosing(imageData, 2);
        
        return imageData;
    }
    
    preserveDetailsClean(imageData) {
        // More conservative cleaning
        imageData = this.convertToGrayscale(imageData);
        
        // Lighter blur
        if (this.settings.blurRadius > 0) {
            imageData = this.gaussianBlur(imageData, Math.max(1, this.settings.blurRadius - 1));
        }
        
        // Adaptive threshold
        imageData = this.adaptiveThreshold(imageData);
        
        // Gentle morphological operations
        imageData = this.morphologyErosion(imageData, Math.max(1, this.settings.erosionSize - 1));
        imageData = this.morphologyDilation(imageData, Math.max(1, this.settings.dilationSize - 1));
        
        // Keep more components
        imageData = this.removeSmallComponents(imageData, this.settings.minComponentSize / 2);
        
        return imageData;
    }
    
    // Image processing helper functions
    cloneImageData(imageData) {
        const cloned = new ImageData(imageData.width, imageData.height);
        cloned.data.set(imageData.data);
        return cloned;
    }
    
    convertToGrayscale(imageData) {
        const data = imageData.data;
        for (let i = 0; i < data.length; i += 4) {
            const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);
            data[i] = gray;     // Red
            data[i + 1] = gray; // Green
            data[i + 2] = gray; // Blue
            // Alpha remains unchanged
        }
        return imageData;
    }
    
    threshold(imageData, thresholdValue) {
        const data = imageData.data;
        for (let i = 0; i < data.length; i += 4) {
            const value = data[i] > thresholdValue ? 255 : 0;
            data[i] = value;     // Red
            data[i + 1] = value; // Green
            data[i + 2] = value; // Blue
        }
        return imageData;
    }
    
    adaptiveThreshold(imageData) {
        const data = imageData.data;
        const width = imageData.width;
        const height = imageData.height;
        
        // Calculate local mean for each pixel
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const idx = (y * width + x) * 4;
                let sum = 0;
                let count = 0;
                
                // 5x5 neighborhood
                for (let dy = -2; dy <= 2; dy++) {
                    for (let dx = -2; dx <= 2; dx++) {
                        const ny = y + dy;
                        const nx = x + dx;
                        if (ny >= 0 && ny < height && nx >= 0 && nx < width) {
                            const nIdx = (ny * width + nx) * 4;
                            sum += data[nIdx];
                            count++;
                        }
                    }
                }
                
                const localMean = sum / count;
                const value = data[idx] > localMean - 10 ? 255 : 0;
                data[idx] = value;
                data[idx + 1] = value;
                data[idx + 2] = value;
            }
        }
        
        return imageData;
    }
    
    gaussianBlur(imageData, radius) {
        if (radius === 0) return imageData;
        
        const data = imageData.data;
        const width = imageData.width;
        const height = imageData.height;
        const output = new Uint8ClampedArray(data);
        
        // Create Gaussian kernel
        const kernel = this.createGaussianKernel(radius);
        const kernelSize = kernel.length;
        const half = Math.floor(kernelSize / 2);
        
        // Apply horizontal blur
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                let sum = 0;
                let weightSum = 0;
                
                for (let i = 0; i < kernelSize; i++) {
                    const px = x + i - half;
                    if (px >= 0 && px < width) {
                        const idx = (y * width + px) * 4;
                        sum += data[idx] * kernel[i];
                        weightSum += kernel[i];
                    }
                }
                
                const result = sum / weightSum;
                const idx = (y * width + x) * 4;
                output[idx] = result;
                output[idx + 1] = result;
                output[idx + 2] = result;
            }
        }
        
        // Apply vertical blur
        for (let x = 0; x < width; x++) {
            for (let y = 0; y < height; y++) {
                let sum = 0;
                let weightSum = 0;
                
                for (let i = 0; i < kernelSize; i++) {
                    const py = y + i - half;
                    if (py >= 0 && py < height) {
                        const idx = (py * width + x) * 4;
                        sum += output[idx] * kernel[i];
                        weightSum += kernel[i];
                    }
                }
                
                const result = sum / weightSum;
                const idx = (y * width + x) * 4;
                data[idx] = result;
                data[idx + 1] = result;
                data[idx + 2] = result;
            }
        }
        
        return imageData;
    }
    
    createGaussianKernel(radius) {
        const size = radius * 2 + 1;
        const kernel = new Array(size);
        const sigma = radius / 3;
        let sum = 0;
        
        for (let i = 0; i < size; i++) {
            const d = i - radius;
            kernel[i] = Math.exp(-d * d / (2 * sigma * sigma));
            sum += kernel[i];
        }
        
        // Normalize
        for (let i = 0; i < size; i++) {
            kernel[i] /= sum;
        }
        
        return kernel;
    }
    
    morphologyErosion(imageData, kernelSize) {
        return this.morphologyOperation(imageData, kernelSize, 'erosion');
    }
    
    morphologyDilation(imageData, kernelSize) {
        return this.morphologyOperation(imageData, kernelSize, 'dilation');
    }
    
    morphologyOpening(imageData, kernelSize) {
        let result = this.morphologyErosion(imageData, kernelSize);
        return this.morphologyDilation(result, kernelSize);
    }
    
    morphologyClosing(imageData, kernelSize) {
        let result = this.morphologyDilation(imageData, kernelSize);
        return this.morphologyErosion(result, kernelSize);
    }
    
    morphologyOperation(imageData, kernelSize, operation) {
        const data = imageData.data;
        const width = imageData.width;
        const height = imageData.height;
        const output = new Uint8ClampedArray(data);
        const half = Math.floor(kernelSize / 2);
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                let result = operation === 'erosion' ? 255 : 0;
                
                // Check kernel neighborhood
                for (let dy = -half; dy <= half; dy++) {
                    for (let dx = -half; dx <= half; dx++) {
                        const ny = y + dy;
                        const nx = x + dx;
                        
                        if (ny >= 0 && ny < height && nx >= 0 && nx < width) {
                            const idx = (ny * width + nx) * 4;
                            const value = data[idx];
                            
                            if (operation === 'erosion') {
                                result = Math.min(result, value);
                            } else {
                                result = Math.max(result, value);
                            }
                        }
                    }
                }
                
                const idx = (y * width + x) * 4;
                output[idx] = result;
                output[idx + 1] = result;
                output[idx + 2] = result;
            }
        }
        
        imageData.data.set(output);
        return imageData;
    }
    
    detectEdges(imageData) {
        const data = imageData.data;
        const width = imageData.width;
        const height = imageData.height;
        const edges = new ImageData(width, height);
        const edgeData = edges.data;
        
        // Sobel edge detection
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                let gx = 0, gy = 0;
                
                // Sobel X kernel
                gx += -1 * data[((y-1) * width + (x-1)) * 4];
                gx += -2 * data[((y) * width + (x-1)) * 4];
                gx += -1 * data[((y+1) * width + (x-1)) * 4];
                gx += 1 * data[((y-1) * width + (x+1)) * 4];
                gx += 2 * data[((y) * width + (x+1)) * 4];
                gx += 1 * data[((y+1) * width + (x+1)) * 4];
                
                // Sobel Y kernel
                gy += -1 * data[((y-1) * width + (x-1)) * 4];
                gy += -2 * data[((y-1) * width + (x)) * 4];
                gy += -1 * data[((y-1) * width + (x+1)) * 4];
                gy += 1 * data[((y+1) * width + (x-1)) * 4];
                gy += 2 * data[((y+1) * width + (x)) * 4];
                gy += 1 * data[((y+1) * width + (x+1)) * 4];
                
                const magnitude = Math.sqrt(gx * gx + gy * gy);
                const value = magnitude > 50 ? 255 : 0;
                
                const idx = (y * width + x) * 4;
                edgeData[idx] = value;
                edgeData[idx + 1] = value;
                edgeData[idx + 2] = value;
                edgeData[idx + 3] = 255;
            }
        }
        
        return edges;
    }
    
    combineWithEdges(imageData, edges) {
        const data = imageData.data;
        const edgeData = edges.data;
        
        for (let i = 0; i < data.length; i += 4) {
            if (edgeData[i] > 0) {
                data[i] = 255;
                data[i + 1] = 255;
                data[i + 2] = 255;
            }
        }
        
        return imageData;
    }
    
    removeSmallComponents(imageData, minSize) {
        const data = imageData.data;
        const width = imageData.width;
        const height = imageData.height;
        const visited = new Array(width * height).fill(false);
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const idx = y * width + x;
                if (!visited[idx] && data[idx * 4] > 0) {
                    const component = this.floodFill(data, width, height, x, y, visited);
                    if (component.length < minSize) {
                        // Remove small component
                        component.forEach(pixelIdx => {
                            data[pixelIdx * 4] = 0;
                            data[pixelIdx * 4 + 1] = 0;
                            data[pixelIdx * 4 + 2] = 0;
                        });
                    }
                }
            }
        }
        
        return imageData;
    }
    
    floodFill(data, width, height, startX, startY, visited) {
        const stack = [{x: startX, y: startY}];
        const component = [];
        
        while (stack.length > 0) {
            const {x, y} = stack.pop();
            const idx = y * width + x;
            
            if (x < 0 || x >= width || y < 0 || y >= height || visited[idx] || data[idx * 4] === 0) {
                continue;
            }
            
            visited[idx] = true;
            component.push(idx);
            
            // Add neighbors
            stack.push({x: x + 1, y: y});
            stack.push({x: x - 1, y: y});
            stack.push({x: x, y: y + 1});
            stack.push({x: x, y: y - 1});
        }
        
        return component;
    }
    
    displayProcessedImage(imageData) {
        const canvas = document.getElementById('processedCanvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = imageData.width;
        canvas.height = imageData.height;
        
        ctx.putImageData(imageData, 0, 0);
    }
    
    // UI helper functions
    showControls() {
        document.getElementById('controlsSection').style.display = 'block';
        document.getElementById('resultsSection').style.display = 'block';
    }
    
    showDownloadSection() {
        document.getElementById('downloadSection').style.display = 'block';
    }
    
    showProcessingIndicator(show) {
        const indicator = document.getElementById('processingIndicator');
        indicator.style.display = show ? 'flex' : 'none';
    }
    
    updateSliderValues() {
        document.getElementById('thresholdValue').textContent = this.settings.threshold;
        document.getElementById('blurValue').textContent = this.settings.blurRadius;
        document.getElementById('erosionValue').textContent = this.settings.erosionSize;
        document.getElementById('dilationValue').textContent = this.settings.dilationSize;
        document.getElementById('componentValue').textContent = this.settings.minComponentSize;
    }
    
    resetSettings() {
        this.settings = {
            threshold: 127,
            blurRadius: 2,
            erosionSize: 3,
            dilationSize: 3,
            minComponentSize: 100,
            processingMode: 'basic'
        };
        
        // Update UI
        document.getElementById('threshold').value = this.settings.threshold;
        document.getElementById('blurRadius').value = this.settings.blurRadius;
        document.getElementById('erosionSize').value = this.settings.erosionSize;
        document.getElementById('dilationSize').value = this.settings.dilationSize;
        document.getElementById('minComponentSize').value = this.settings.minComponentSize;
        document.getElementById('processingMode').value = this.settings.processingMode;
        
        this.updateSliderValues();
        this.processImage();
    }
    
    downloadImage() {
        if (!this.processedImageData) return;
        
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = this.processedImageData.width;
        canvas.height = this.processedImageData.height;
        ctx.putImageData(this.processedImageData, 0, 0);
        
        canvas.toBlob((blob) => {
            const link = document.createElement('a');
            link.download = 'cleaned-floorplan.png';
            link.href = URL.createObjectURL(blob);
            link.click();
            URL.revokeObjectURL(link.href);
        });
    }
    
    resetApplication() {
        // Reset all state
        this.originalImage = null;
        this.originalImageData = null;
        this.processedImageData = null;
        
        // Hide sections
        document.getElementById('controlsSection').style.display = 'none';
        document.getElementById('resultsSection').style.display = 'none';
        document.getElementById('downloadSection').style.display = 'none';
        
        // Clear canvases
        const originalCanvas = document.getElementById('originalCanvas');
        const processedCanvas = document.getElementById('processedCanvas');
        originalCanvas.getContext('2d').clearRect(0, 0, originalCanvas.width, originalCanvas.height);
        processedCanvas.getContext('2d').clearRect(0, 0, processedCanvas.width, processedCanvas.height);
        
        // Reset file input
        document.getElementById('fileInput').value = '';
        
        this.hideError();
        this.resetSettings();
    }
    
    showError(message) {
        const errorElement = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        errorText.textContent = message;
        errorElement.style.display = 'block';
        
        // Auto-hide after 5 seconds
        setTimeout(() => this.hideError(), 5000);
    }
    
    hideError() {
        document.getElementById('errorMessage').style.display = 'none';
    }
    
    showStatus(message, type) {
        const statusElement = document.getElementById('uploadStatus');
        statusElement.innerHTML = `<div class="status status--${type}">${message}</div>`;
        
        if (type === 'success') {
            setTimeout(() => {
                statusElement.innerHTML = '';
            }, 3000);
        }
    }
    
    // Debounce processing for real-time updates
    debounceProcess() {
        clearTimeout(this.processTimeout);
        this.processTimeout = setTimeout(() => {
            this.processImage();
        }, 300);
    }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new FloorPlanCleaner();
});